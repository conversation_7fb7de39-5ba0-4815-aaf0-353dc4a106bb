# coding:utf-8
# excel_auto_crawler.py
"""
Excel自动化爬取模块
功能：从Excel文件读取微信文章链接，使用uiautomation自动打开链接并触发Cookie抓取
"""

import os
import time
import pandas as pd
import logging
from read_cookie import ReadCookie
from batch_readnum_spider import BatchReadnumSpider

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 导入微信浏览器自动化模块
try:
    from wechat_browser_automation import WeChatBrowserAutomation, UI_AUTOMATION_AVAILABLE
except ImportError:
    WeChatBrowserAutomation = None
    UI_AUTOMATION_AVAILABLE = False
    logging.warning("wechat_browser_automation.py导入失败，UI自动化功能将不可用。")


class ExcelAutoCrawler:
    """Excel自动化爬取器"""
    
    def __init__(self, excel_file="target_articles.xlsx"):
        self.excel_file = excel_file
        self.cookie_reader = ReadCookie()
        self.spider = BatchReadnumSpider()
        self.wechat_automation = None

        logging.info(f"Excel自动化爬取器已初始化，操作文件: {excel_file}")
        if UI_AUTOMATION_AVAILABLE:
            try:
                self.wechat_automation = WeChatBrowserAutomation()
                logging.info("微信UI自动化模块加载成功。")
            except Exception as e: # Catch any other potential exceptions during instantiation
                logging.error(f"初始化WeChatBrowserAutomation失败: {e}")
                self.wechat_automation = None
        else:
            logging.warning("UI自动化库(uiautomation)或模块不可用，需要手动操作微信来抓取Cookie。")
        
    def create_sample_excel(self):
        """创建示例Excel文件"""
        sample_data = {
            '公众号名称': ['示例公众号'],
            '示例文章链接': ['https://mp.weixin.qq.com/s/Y_IAb3m5d2gZOFk3s6o74g'], # 请替换
            '备注': [
                '请将此处的链接替换为目标公众号的任意一篇有效文章链接。',
                '程序将通过此链接自动在微信内置浏览器中打开文章，以抓取爬取所需的Cookie。'
            ]
        }
        df = pd.DataFrame(sample_data)
        df.to_excel(self.excel_file, index=False, engine='openpyxl')
        logging.info(f"已创建示例Excel文件: {self.excel_file}，请填入实际链接后重新运行。")
        
    def read_excel_data(self):
        """从Excel文件读取数据"""
        if not os.path.exists(self.excel_file):
            logging.warning(f"Excel文件不存在: {self.excel_file}，正在创建示例文件...")
            self.create_sample_excel()
            return []
            
        try:
            df = pd.read_excel(self.excel_file, engine='openpyxl')
            required_columns = ['公众号名称', '示例文章链接']
            if not all(col in df.columns for col in required_columns):
                logging.error(f"Excel文件缺少必要列: {required_columns}。")
                return []
                
            valid_data = []
            for index, row in df.iterrows():
                account_name = str(row['公众号名称']).strip()
                article_url = str(row['示例文章链接']).strip()
                if account_name and 'mp.weixin.qq.com' in article_url:
                    valid_data.append({'account_name': account_name, 'article_url': article_url})
            
            logging.info(f"从Excel读取到 {len(valid_data)} 条有效数据。")
            return valid_data
        except Exception as e:
            logging.error(f"读取Excel文件失败: {e}")
            return []
    
    def wait_for_cookie_capture(self, timeout=60):
        """等待Cookie抓取器捕获到新的Cookie"""
        logging.info(f"等待Cookie抓取完成（超时时间: {timeout}秒）...")
        logging.info("请确保 mitmproxy 正在运行，并且微信代理已正确设置为 127.0.0.1:8080。")

        initial_cookie = self.cookie_reader.get_latest_cookies()
        start_time = time.time()

        while time.time() - start_time < timeout:
            latest_cookie = self.cookie_reader.get_latest_cookies()
            if latest_cookie and latest_cookie != initial_cookie:
                logging.info(f"成功捕获到新的Cookie！(时间戳: {latest_cookie.get('timestamp')})")
                return True
            time.sleep(2)
            print(".", end="", flush=True)
        
        print() # 换行
        logging.warning(f"在 {timeout} 秒内未检测到新的Cookie。")
        logging.warning("请检查代理设置并手动在微信中刷新文章页面。")
        return False
    
    def start_cookie_extractor_background(self):
        """在后台启动Cookie抓取器"""
        logging.info("正在后台启动Cookie抓取器 (mitmproxy)...")
        try:
            if self.cookie_reader.start_cookie_extractor(timeout=10):
                logging.info("Cookie抓取器启动成功。")
                return True
            else:
                logging.error("Cookie抓取器启动失败，请检查mitmproxy是否正确安装及环境配置。")
                return False
        except Exception as e:
            logging.error(f"启动Cookie抓取器时发生异常: {e}")
            return False
    
    def auto_crawl_from_excel(self):
        """从Excel自动化爬取流程"""
        logging.info("="*60)
        logging.info("🚀 Excel自动化爬取流程启动 🚀")
        logging.info("="*60)
        
        excel_data = self.read_excel_data()
        if not excel_data:
            return
            
        logging.info("待处理的公众号列表:")
        for item in excel_data:
            logging.info(f"  - {item['account_name']}: {item['article_url'][:70]}...")
            
        if not self.start_cookie_extractor_background():
            return

        first_article_url = excel_data[0]['article_url']
        logging.info(f"将使用第一个链接进行UI自动化以获取Cookie: {first_article_url}")

        if self.wechat_automation:
            logging.info("执行微信UI自动化以打开文章并刷新...")
            # 调用重构后的自动化模块，刷新5次
            success = self.wechat_automation.run(first_article_url, refresh_count=5)
            if not success:
                logging.error("微信UI自动化执行失败。")
        
            
        if not self.wait_for_cookie_capture():
            return
            
        logging.info("Cookie已获取，开始批量爬取所有公众号的文章信息...")
        self.batch_crawl_all_accounts(excel_data)

    
    
    def batch_crawl_all_accounts(self, excel_data):
        """使用获取到的Cookie，批量爬取所有公众号的文章"""
        logging.info("="*60)
        logging.info(f"准备批量爬取 {len(excel_data)} 个公众号...")
        
        all_articles = []
        for i, account_info in enumerate(excel_data, 1):
            logging.info(f"----> 正在处理第 {i}/{len(excel_data)} 个: {account_info['account_name']} <----")
            
            # 从文章链接中提取__biz参数
            biz = self.spider.extract_biz_from_url(account_info['article_url'])
            if not biz:
                logging.warning(f"无法从链接中提取公众号标识(__biz)，跳过: {account_info['account_name']}")
                continue

            # 使用最新的Cookie和提取的biz进行爬取
            # 注意：这里假设 batch_crawl_readnum 内部会使用最新的Cookie
            # 我们需要确保spider能获取到最新的cookie信息
            self.spider.biz = biz # 传递biz
            
            # 爬取最近30天的文章，最多2页
            articles = self.spider.batch_crawl_readnum(max_pages=2, days_back=30)
            
            if articles:
                for article in articles:
                    article['公众号名称'] = account_info['account_name']
                all_articles.extend(articles)
                logging.info(f"成功获取 {len(articles)} 篇文章。")
            else:
                logging.warning(f"未能获取到文章，可能没有新文章或抓取失败。")
            
            if i < len(excel_data):
                delay = 5
                logging.info(f"延时 {delay} 秒后继续下一个...")
                time.sleep(delay)
        
        if all_articles:
            logging.info("="*60)
            logging.info(f"🎉 批量爬取完成！总共获取 {len(all_articles)} 篇文章。")
            self.spider.articles_data = all_articles
            excel_file = self.spider.save_to_excel()
            json_file = self.spider.save_to_json()
            logging.info(f"结果已保存到: {excel_file} 和 {json_file}")
        else:
            logging.warning("所有任务完成，但未获取到任何文章数据。")

def main():
    """主函数"""
    try:
        crawler = ExcelAutoCrawler()
        crawler.auto_crawl_from_excel()
    except Exception as e:
        logging.error(f"程序发生未处理的异常: {e}", exc_info=True)

if __name__ == "__main__":
    main()
