# coding:utf-8
# wechat_browser_automation.py
"""
微信内置浏览器自动化模块
专门用于在微信PC版中自动打开和刷新微信文章链接
"""

import time
import pyperclip
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 尝试导入uiautomation
try:
    import uiautomation as auto
    UI_AUTOMATION_AVAILABLE = True
except ImportError:
    UI_AUTOMATION_AVAILABLE = False
    logging.error("uiautomation库未安装，无法执行UI自动化。请运行: pip install uiautomation")

class WeChatBrowserAutomation:
    """微信内置浏览器自动化控制器"""
    
    def __init__(self):
        # 移除这里的raise ImportError，让类始终可以被实例化
        self.wechat_window = None
        self.browser_window = None
        if UI_AUTOMATION_AVAILABLE:
            # 设置全局搜索超时，仅当uiautomation可用时设置
            auto.SetGlobalSearchTimeout(15) 
        else:
            logging.warning("uiautomation库不可用，WeChatBrowserAutomation的功能将受限。")

    def _check_automation_available(self) -> bool:
        """内部方法：检查uiautomation是否可用"""
        if not UI_AUTOMATION_AVAILABLE:
            logging.error("UI自动化功能不可用，请确保uiautomation库已正确安装。")
            return False
        return True

    def find_wechat_window(self) -> auto.WindowControl:
        """
        查找并返回微信PC版主窗口。
        使用更可靠的复合条件进行搜索。
        """
        if not self._check_automation_available():
            return None

        logging.info("正在查找微信主窗口...")
        # 微信窗口的类名通常是 'WeChatMainWndForPC'
        self.wechat_window = auto.WindowControl(ClassName='WeChatMainWndForPC')
        if self.wechat_window.Exists(10): 
            logging.info("成功找到微信窗口 (ClassName='WeChatMainWndForPC')")
            return self.wechat_window
        
        logging.warning("未找到 'WeChatMainWndForPC' 窗口，尝试备用方案...")
        # 备用方案：通过窗口标题“微信”查找
        self.wechat_window = auto.WindowControl(searchDepth=1, Name='微信')
        if self.wechat_window.Exists(5): 
            logging.info("成功找到微信窗口 (Name='微信')")
            return self.wechat_window
            
        logging.error("未找到微信主窗口，请确保微信已登录并显示主界面。")
        return None

    def activate_wechat(self) -> bool:
        """激活微信窗口并置顶"""
        if not self._check_automation_available():
            return False

        if not self.wechat_window or not self.wechat_window.Exists(1):
            if not self.find_wechat_window():
                return False
        
        try:
            logging.info("正在激活微信窗口...")
            self.wechat_window.SetActive()
            self.wechat_window.SetTopmost(True)
            time.sleep(1) 
            self.wechat_window.SetTopmost(False)
            logging.info("微信窗口已激活。")
            return True
        except Exception as e:
            logging.error(f"激活微信窗口失败: {e}")
            return False

    def send_link_to_file_transfer(self, article_url: str) -> bool:
        """
        将文章链接发送到文件传输助手。
        """
        if not self._check_automation_available():
            return False

        logging.info("准备将链接发送到文件传输助手...")
        if not self.activate_wechat():
            return False

        # 1. 尝试通过搜索框查找并进入“文件传输助手”
        try:
            logging.info("尝试通过搜索框查找并进入'文件传输助手'...")
            search_box = self.wechat_window.EditControl(Name='搜索')
            if search_box.Exists(5): 
                search_box.Click(simulateMove=True)
                search_box.SetValue('文件传输助手')
                auto.SendKeys('{Enter}')
                time.sleep(2) 
                logging.info("已通过搜索进入'文件传输助手'。")
            else:
                logging.warning("未找到搜索框，尝试直接在聊天列表中查找'文件传输助手'。")
                # Fallback: direct click in chat list
                chat_list = self.wechat_window.ListControl(Name='会话')
                file_transfer_item = chat_list.ListItemControl(Name='文件传输助手')
                if file_transfer_item.Exists(5): 
                    logging.info("在聊天列表中找到'文件传输助手'，正在点击...")
                    file_transfer_item.Click(simulateMove=True)
                    time.sleep(1)
                else:
                    logging.error("未能找到'文件传输助手'。请确保微信已登录且'文件传输助手'可见。")
                    return False
        except Exception as e:
            logging.error(f"查找或进入'文件传输助手'失败: {e}")
            return False

        # 2. 将链接粘贴到输入框并发送
        try:
            # Try to find the input box more flexibly
            input_box = self.wechat_window.EditControl(searchDepth=5) 
            if not input_box.Exists(10): 
                logging.error("未找到聊天输入框。")
                return False
            
            logging.info("找到输入框，准备发送链接...")
            input_box.Click(simulateMove=True)
            
            pyperclip.copy(article_url)
            logging.info(f"已将链接复制到剪贴板: {article_url}")
            input_box.SendKeys('{Ctrl}v', waitTime=1)
            
            auto.SendKeys('{Enter}')
            logging.info("链接已发送。")
            time.sleep(3) 
            return True
        except Exception as e:
            logging.error(f"发送链接到输入框失败: {e}")
            return False

    def click_sent_link(self) -> bool:
        if not self._check_automation_available():
            return False

        logging.info("正在查找并点击已发送的链接...")
        try:
            # Try to find the message list more robustly
            message_list = self.wechat_window.ListControl(searchDepth=5) 
            if not message_list.Exists(5):
                logging.warning("未找到消息列表，尝试直接在窗口中查找链接。")
                # Fallback to searching directly in the window if message list not found
                link_control = self.wechat_window.TextControl(SubName='mp.weixin.qq.com')
                if link_control.WaitForExist(15): 
                    logging.info("在窗口中找到链接，正在点击...")
                    link_control.Click(simulateMove=True)
                    time.sleep(5)
                    return True
                else:
                    logging.error("在窗口中未找到发送的链接。")
                    return False

            # If message list found, search within it
            link_control = message_list.TextControl(SubName='mp.weixin.qq.com')
            
            if link_control.WaitForExist(15): 
                logging.info("在消息列表中找到链接，正在点击...")
                link_control.Click(simulateMove=True)
                time.sleep(5) 
                return True
            else:
                logging.error("在聊天记录中未找到发送的链接。")
                return False
        except Exception as e:
            logging.error(f"点击链接失败: {e}")
            return False

    def find_and_refresh_browser(self, refresh_count: int = 3, interval: int = 3) -> bool:
        if not self._check_automation_available():
            return False

        logging.info("正在查找微信内置浏览器窗口...")
        # More robust browser window identification
        browser_class_names = ['CefWebViewWnd', 'Chrome_WidgetWin_1', 'WeChatWebview']
        
        self.browser_window = None
        for class_name in browser_class_names:
            self.browser_window = auto.WindowControl(ClassName=class_name)
            if self.browser_window.Exists(5):
                logging.info(f"成功找到浏览器窗口 (ClassName='{class_name}')")
                break
        
        if not self.browser_window or not self.browser_window.Exists(3):
            logging.error("未找到微信内置浏览器窗口。请确认链接已在微信中打开。")
            return False
        
        logging.info(f"找到浏览器窗口，将刷新 {refresh_count} 次...")
        try:
            self.browser_window.SetActive()
            for i in range(refresh_count):
                logging.info(f"第 {i+1}/{refresh_count} 次刷新...")
                self.browser_window.SendKeys('{F5}')
                time.sleep(interval)
            logging.info("刷新完成。")
            return True
        except Exception as e:
            logging.error(f"刷新浏览器失败: {e}")
            return False

    def run(self, article_url: str, refresh_count: int = 3) -> bool:
        """
        执行完整的自动化流程：打开微信 -> 发送链接 -> 点击链接 -> 刷新浏览器
        """
        if not self._check_automation_available():
            logging.error("UI自动化功能不可用，无法执行自动化流程。")
            return False

        logging.info("开始执行微信浏览器自动化全流程...")
        
        if not self.send_link_to_file_transfer(article_url):
            logging.error("发送链接失败，流程中止。")
            return False
            
        if not self.click_sent_link():
            logging.error("点击链接失败，流程中止。")
            return False
            
        if not self.find_and_refresh_browser(refresh_count=refresh_count):
            logging.error("刷新浏览器失败，流程中止。")
            return False
            
        logging.info("微信浏览器自动化流程成功执行完毕！")
        return True

def main():
    """主函数，用于测试自动化模块"""
    logging.info("开始测试微信浏览器自动化模块...")
    
    # --- 重要：请在此处填入一个有效的微信文章链接 ---
    test_article_url = "https://mp.weixin.qq.com/s/Y_IAb3m5d2gZOFk3s6o74g" # 这是一个示例链接，请替换
    
    if "Y_IAb3m5d2gZOFk3s6o74g" in test_article_url:
        logging.warning("请将 `test_article_url` 替换为真实的微信文章链接后再运行测试。")
        return

    try:
        automation = WeChatBrowserAutomation()
        # 执行自动化流程，刷新3次
        success = automation.run(test_article_url, refresh_count=3)
        if success:
            logging.info("自动化测试成功！")
        else:
            logging.error("自动化测试失败。")
    except Exception as e:
        logging.error(f"测试过程中发生未知错误: {e}")

if __name__ == "__main__":
    main()