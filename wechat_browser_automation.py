# coding:utf-8
# wechat_browser_automation.py
"""
微信内置浏览器自动化模块
专门用于在微信PC版中自动打开和刷新微信文章链接
"""

import time
import pyperclip
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 尝试导入uiautomation
try:
    import uiautomation as auto
    UI_AUTOMATION_AVAILABLE = True
except ImportError:
    UI_AUTOMATION_AVAILABLE = False
    logging.error("uiautomation库未安装，无法执行UI自动化。请运行: pip install uiautomation")

class WeChatBrowserAutomation:
    """微信内置浏览器自动化控制器"""
    
    def __init__(self):
        # 移除这里的raise ImportError，让类始终可以被实例化
        self.wechat_window = None
        self.browser_window = None
        if UI_AUTOMATION_AVAILABLE:
            # 设置全局搜索超时，仅当uiautomation可用时设置
            auto.SetGlobalSearchTimeout(15) 
        else:
            logging.warning("uiautomation库不可用，WeChatBrowserAutomation的功能将受限。")

    def _check_automation_available(self) -> bool:
        """内部方法：检查uiautomation是否可用"""
        if not UI_AUTOMATION_AVAILABLE:
            logging.error("UI自动化功能不可用，请确保uiautomation库已正确安装。")
            return False
        return True

    def find_wechat_window(self) -> auto.WindowControl:
        """
        查找并返回微信PC版主窗口。
        使用更可靠的复合条件进行搜索。
        """
        if not self._check_automation_available():
            return None

        logging.info("正在查找微信主窗口...")
        # 微信窗口的类名通常是 'WeChatMainWndForPC'
        self.wechat_window = auto.WindowControl(ClassName='WeChatMainWndForPC')
        if self.wechat_window.Exists(10): 
            logging.info("成功找到微信窗口 (ClassName='WeChatMainWndForPC')")
            return self.wechat_window
        
        logging.warning("未找到 'WeChatMainWndForPC' 窗口，尝试备用方案...")
        # 备用方案：通过窗口标题“微信”查找
        self.wechat_window = auto.WindowControl(searchDepth=1, Name='微信')
        if self.wechat_window.Exists(5): 
            logging.info("成功找到微信窗口 (Name='微信')")
            return self.wechat_window
            
        logging.error("未找到微信主窗口，请确保微信已登录并显示主界面。")
        return None

    def activate_wechat(self) -> bool:
        """激活微信窗口并置顶"""
        if not self._check_automation_available():
            return False

        if not self.wechat_window or not self.wechat_window.Exists(1):
            if not self.find_wechat_window():
                return False
        
        try:
            logging.info("正在激活微信窗口...")
            self.wechat_window.SetActive()
            self.wechat_window.SetTopmost(True)
            time.sleep(1) 
            self.wechat_window.SetTopmost(False)
            logging.info("微信窗口已激活。")
            return True
        except Exception as e:
            logging.error(f"激活微信窗口失败: {e}")
            return False

    def send_link_to_file_transfer(self, article_url: str) -> bool:
        """
        将文章链接发送到文件传输助手。
        """
        if not self._check_automation_available():
            return False

        logging.info("准备将链接发送到文件传输助手...")
        if not self.activate_wechat():
            return False

        # 1. 尝试通过搜索框查找并进入“文件传输助手”
        try:
            logging.info("尝试通过搜索框查找并进入'文件传输助手'...")
            search_box = self.wechat_window.EditControl(Name='搜索')
            if search_box.Exists(5):
                search_box.Click(simulateMove=True)
                time.sleep(0.5)
                # 清空搜索框并输入文本
                search_box.SendKeys('{Ctrl}a')
                time.sleep(0.2)
                search_box.SendKeys('文件传输助手')
                time.sleep(0.5)
                auto.SendKeys('{Enter}')
                time.sleep(3)
                logging.info("已通过搜索进入'文件传输助手'。")

                # 重要：清空搜索框并点击聊天区域，确保焦点离开搜索框
                logging.info("清空搜索框并将焦点转移到聊天区域...")
                search_box.SendKeys('{Ctrl}a')
                time.sleep(0.2)
                search_box.SendKeys('{Delete}')
                time.sleep(0.5)

                # 点击聊天区域中央，确保焦点离开搜索框
                rect = self.wechat_window.BoundingRectangle
                chat_area_x = rect.left + (rect.right - rect.left) // 2
                chat_area_y = rect.top + (rect.bottom - rect.top) // 2
                auto.Click(chat_area_x, chat_area_y)
                time.sleep(1)
            else:
                logging.warning("未找到搜索框，尝试直接在聊天列表中查找'文件传输助手'。")
                # Fallback: direct click in chat list
                chat_list = self.wechat_window.ListControl(Name='会话')
                file_transfer_item = chat_list.ListItemControl(Name='文件传输助手')
                if file_transfer_item.Exists(5): 
                    logging.info("在聊天列表中找到'文件传输助手'，正在点击...")
                    file_transfer_item.Click(simulateMove=True)
                    time.sleep(1)
                else:
                    logging.error("未能找到'文件传输助手'。请确保微信已登录且'文件传输助手'可见。")
                    return False
        except Exception as e:
            logging.error(f"查找或进入'文件传输助手'失败: {e}")
            return False

        # 2. 将链接粘贴到输入框并发送
        try:
            logging.info("正在查找聊天输入框...")

            # 等待界面稳定
            time.sleep(2)

            # 尝试多种方式查找输入框
            input_box = None

            # 方法1: 查找所有EditControl，排除搜索框
            logging.info("方法1: 查找所有EditControl，排除搜索框...")
            edit_controls = []
            try:
                # 获取所有EditControl
                all_controls = self.wechat_window.GetChildren()
                for control in all_controls:
                    if hasattr(control, 'ControlType') and 'Edit' in str(control.ControlType):
                        # 检查是否是搜索框（通过Name属性）
                        if hasattr(control, 'Name') and control.Name != '搜索':
                            edit_controls.append(control)
                            logging.info(f"找到非搜索框的EditControl: {control.Name}")

                # 选择最后一个（通常是聊天输入框）
                if edit_controls:
                    input_box = edit_controls[-1]
                    logging.info("通过排除搜索框找到聊天输入框")
            except Exception as e:
                logging.warning(f"方法1失败: {e}")

            # 方法2: 如果方法1失败，尝试通过位置查找
            if not input_box:
                logging.info("方法2: 直接点击聊天输入区域...")
                rect = self.wechat_window.BoundingRectangle
                # 点击窗口底部中央位置（聊天输入区域）
                click_x = rect.left + (rect.right - rect.left) // 2
                click_y = rect.bottom - 80  # 距离底部80像素，避开发送按钮

                logging.info(f"点击聊天输入区域坐标: ({click_x}, {click_y})")
                auto.Click(click_x, click_y)
                time.sleep(1)

                # 确保输入框获得焦点后再粘贴
                pyperclip.copy(article_url)
                logging.info(f"已将链接复制到剪贴板: {article_url}")

                # 清空可能存在的内容
                auto.SendKeys('{Ctrl}a')
                time.sleep(0.2)
                auto.SendKeys('{Ctrl}v')
                time.sleep(1)
                auto.SendKeys('{Enter}')
                logging.info("通过点击输入区域发送链接。")
                time.sleep(3)
                return True

            if input_box and input_box.Exists(2):
                logging.info("找到输入框，准备发送链接...")
                input_box.Click(simulateMove=True)
                time.sleep(0.5)

                pyperclip.copy(article_url)
                logging.info(f"已将链接复制到剪贴板: {article_url}")
                input_box.SendKeys('{Ctrl}v')
                time.sleep(1)
                auto.SendKeys('{Enter}')
                logging.info("链接已发送。")
                time.sleep(3)
                return True
            else:
                logging.error("所有方法都未能找到聊天输入框。")
                return False

        except Exception as e:
            logging.error(f"发送链接到输入框失败: {e}")
            return False

    def click_sent_link(self) -> bool:
        if not self._check_automation_available():
            return False

        logging.info("正在查找并点击已发送的链接...")
        try:
            # 等待链接出现在聊天中
            time.sleep(2)

            # 方法1: 直接在窗口中查找包含微信链接的文本控件
            logging.info("方法1: 在窗口中查找微信链接...")
            link_patterns = ['mp.weixin.qq.com', 'weixin.qq.com', '微信文章']

            for pattern in link_patterns:
                link_control = self.wechat_window.TextControl(SubName=pattern, searchDepth=10)
                if link_control.Exists(5):
                    logging.info(f"找到链接控件 (模式: {pattern})，正在点击...")
                    link_control.Click(simulateMove=True)
                    time.sleep(3)
                    logging.info("链接点击完成，准备查找浏览器窗口...")
                    return True

            # 方法2: 查找消息列表中的链接
            logging.info("方法2: 在消息列表中查找链接...")
            message_list = self.wechat_window.ListControl(searchDepth=8)
            if message_list.Exists(3):
                for pattern in link_patterns:
                    link_control = message_list.TextControl(SubName=pattern, searchDepth=5)
                    if link_control.Exists(3):
                        logging.info(f"在消息列表中找到链接 (模式: {pattern})，正在点击...")
                        link_control.Click(simulateMove=True)
                        time.sleep(3)
                        logging.info("链接点击完成，准备查找浏览器窗口...")
                        return True

            # 方法3: 查找所有文本控件，寻找包含链接的控件
            logging.info("方法3: 遍历所有文本控件查找链接...")
            all_controls = self.wechat_window.GetChildren()
            for control in all_controls:
                try:
                    if hasattr(control, 'Name') and control.Name:
                        control_name = str(control.Name).lower()
                        if 'mp.weixin.qq.com' in control_name or 'weixin.qq.com' in control_name:
                            logging.info(f"通过遍历找到链接控件: {control.Name[:50]}...")
                            control.Click(simulateMove=True)
                            time.sleep(3)
                            logging.info("链接点击完成，准备查找浏览器窗口...")
                            return True
                except:
                    continue

            # 方法4: 如果都找不到，尝试双击聊天区域的最后一条消息
            logging.info("方法4: 尝试双击聊天区域...")
            rect = self.wechat_window.BoundingRectangle
            # 点击聊天区域中央偏下的位置
            click_x = rect.left + (rect.right - rect.left) // 2
            click_y = rect.bottom - 150  # 距离底部150像素
            auto.Click(click_x, click_y)
            time.sleep(0.5)
            auto.Click(click_x, click_y)  # 双击
            time.sleep(3)

            logging.warning("未能找到明确的链接控件，已尝试双击聊天区域")
            return True  # 假设双击成功

        except Exception as e:
            logging.error(f"点击链接失败: {e}")
            return False

    def find_and_refresh_browser(self, refresh_count: int = 3, interval: int = 3) -> bool:
        if not self._check_automation_available():
            return False

        logging.info("正在查找微信内置浏览器窗口...")

        # 等待浏览器窗口出现（给更多时间）
        time.sleep(5)

        # 更全面的浏览器窗口类名列表
        browser_class_names = [
            'CefWebViewWnd',           # CEF浏览器窗口
            'Chrome_WidgetWin_1',      # Chrome内核窗口
            'WeChatWebview',           # 微信网页视图
            'WebView2',                # Edge WebView2
            'WebBrowser',              # 通用浏览器控件
            'Internet Explorer_Server', # IE内核
            'Shell DocObject View',     # Shell文档视图
        ]

        self.browser_window = None

        # 方法1: 通过类名查找
        for class_name in browser_class_names:
            logging.info(f"尝试查找浏览器窗口: {class_name}")
            self.browser_window = auto.WindowControl(ClassName=class_name)
            if self.browser_window.Exists(3):
                logging.info(f"成功找到浏览器窗口 (ClassName='{class_name}')")
                break

        # 方法2: 如果通过类名找不到，尝试查找包含微信相关标题的窗口
        if not self.browser_window or not self.browser_window.Exists(2):
            logging.info("通过类名未找到，尝试查找包含微信标题的窗口...")
            title_patterns = ['微信', '公众号', 'WeChat', 'mp.weixin.qq.com']

            for pattern in title_patterns:
                self.browser_window = auto.WindowControl(SubName=pattern)
                if self.browser_window.Exists(3):
                    logging.info(f"通过标题找到浏览器窗口 (标题包含: {pattern})")
                    break

        # 方法3: 查找所有窗口，寻找最近打开的浏览器类型窗口
        if not self.browser_window or not self.browser_window.Exists(2):
            logging.info("尝试遍历所有窗口查找浏览器...")
            desktop = auto.GetRootControl()
            all_windows = desktop.GetChildren()

            for window in all_windows:
                try:
                    if hasattr(window, 'ClassName') and window.ClassName:
                        class_name = str(window.ClassName).lower()
                        if any(browser_type in class_name for browser_type in ['chrome', 'webview', 'browser', 'cef']):
                            logging.info(f"通过遍历找到可能的浏览器窗口: {window.ClassName}")
                            self.browser_window = window
                            break
                except:
                    continue

        # 如果还是找不到，尝试直接对微信窗口发送F5
        if not self.browser_window or not self.browser_window.Exists(2):
            logging.warning("未找到独立的浏览器窗口，尝试直接对微信窗口发送F5...")
            if self.wechat_window and self.wechat_window.Exists(2):
                try:
                    self.wechat_window.SetActive()
                    time.sleep(1)
                    for i in range(refresh_count):
                        logging.info(f"对微信窗口进行第 {i+1}/{refresh_count} 次F5刷新...")
                        self.wechat_window.SendKeys('{F5}')
                        time.sleep(interval)
                    logging.info("微信窗口F5刷新完成。")
                    return True
                except Exception as e:
                    logging.error(f"对微信窗口发送F5失败: {e}")

            logging.error("所有方法都未能找到可刷新的浏览器窗口。")
            return False

        # 找到浏览器窗口，执行刷新
        logging.info(f"找到浏览器窗口，将刷新 {refresh_count} 次...")
        try:
            self.browser_window.SetActive()
            time.sleep(1)
            for i in range(refresh_count):
                logging.info(f"第 {i+1}/{refresh_count} 次刷新...")
                self.browser_window.SendKeys('{F5}')
                time.sleep(interval)
            logging.info("浏览器刷新完成。")
            return True
        except Exception as e:
            logging.error(f"刷新浏览器失败: {e}")
            return False

    def run(self, article_url: str, refresh_count: int = 3) -> bool:
        """
        执行完整的自动化流程：打开微信 -> 发送链接 -> 点击链接 -> 刷新浏览器
        """
        if not self._check_automation_available():
            logging.error("UI自动化功能不可用，无法执行自动化流程。")
            return False

        logging.info("开始执行微信浏览器自动化全流程...")
        
        if not self.send_link_to_file_transfer(article_url):
            logging.error("发送链接失败，流程中止。")
            return False
            
        if not self.click_sent_link():
            logging.error("点击链接失败，流程中止。")
            return False
            
        if not self.find_and_refresh_browser(refresh_count=refresh_count):
            logging.error("刷新浏览器失败，流程中止。")
            return False
            
        logging.info("微信浏览器自动化流程成功执行完毕！")
        return True

def main():
    """主函数，用于测试自动化模块"""
    logging.info("开始测试微信浏览器自动化模块...")
    
    # --- 重要：请在此处填入一个有效的微信文章链接 ---
    test_article_url = "https://mp.weixin.qq.com/s/Y_IAb3m5d2gZOFk3s6o74g" # 这是一个示例链接，请替换
    
    if "Y_IAb3m5d2gZOFk3s6o74g" in test_article_url:
        logging.warning("请将 `test_article_url` 替换为真实的微信文章链接后再运行测试。")
        return

    try:
        automation = WeChatBrowserAutomation()
        # 执行自动化流程，刷新3次
        success = automation.run(test_article_url, refresh_count=3)
        if success:
            logging.info("自动化测试成功！")
        else:
            logging.error("自动化测试失败。")
    except Exception as e:
        logging.error(f"测试过程中发生未知错误: {e}")

if __name__ == "__main__":
    main()