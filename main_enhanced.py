# coding:utf-8
# main_enhanced.py
"""
微信公众号爬虫工具集 v2.5
集成微信UI自动化，实现从Excel启动的批量爬取。
"""

import os
import traceback
from read_cookie import ReadCookie
from batch_readnum_spider import BatchReadnumSpider
from excel_auto_crawler import ExcelAutoCrawler

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("      🚀 微信公众号爬虫工具集 v2.5 🚀")
    print("="*60)
    print("1. 抓取Cookie (手动模式)")
    print("2. 批量爬取文章 (使用已有Cookie)")
    print("3. 【推荐】从Excel启动全自动爬取")
    print("4. 退出程序")
    print("="*60)
    print("💡 v2.5 更新: 重构UI自动化流程，提升稳定性和用户体验。")

def extract_cookies():
    """手动抓取Cookie"""
    print("\n🔧 启动手动Cookie抓取器...")
    cookie_reader = ReadCookie()
    
    # 启动抓取器
    if cookie_reader.start_cookie_extractor(timeout=10):
        print("\n抓取器已启动。请手动在微信中打开任意公众号文章并刷新几次。")
        print("等待抓取完成...")
    else:
        print("抓取器启动失败，请检查mitmproxy是否安装。")
        return

    # 解析cookie
    if cookie_reader.wait_for_new_cookie(timeout=120):
        result = cookie_reader.get_latest_cookies()
        print("\n" + "="*50)
        print("✅ Cookie解析成功:")
        print(f"   __biz: {result['biz']}")
        print(f"   appmsg_token: {result['appmsg_token'][:20]}...")
        print(f"   解析时间: {result['timestamp']}")
        print("="*50)
    else:
        print("❌ Cookie抓取超时或失败。")

def batch_readnum_crawler():
    """批量文章内容+统计数据抓取"""
    print("\n📊 启动批量爬取器 (使用已有Cookie)...")
    
    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到 wechat_keys.txt 文件。请先运行选项1或3抓取Cookie。")
        return
    
    try:
        max_pages = int(input("最大页数 (默认3): ") or "3")
        days_back = int(input("抓取多少天内的文章 (默认7): ") or "7")
    except ValueError:
        print("❌ 参数输入无效，使用默认值。")
        max_pages, days_back = 3, 7
    
    spider = BatchReadnumSpider()
    try:
        print(f"\n🚀 开始批量抓取: {max_pages}页, {days_back}天内文章。")
        spider.batch_crawl_readnum(max_pages=max_pages, days_back=days_back)
        
        if spider.articles_data:
            spider.print_summary()
            excel_file = spider.save_to_excel()
            json_file = spider.save_to_json()
            print(f"\n🎉 抓取完成！结果已保存到 {excel_file} 和 {json_file}")
        else:
            print("❌ 未获取到任何数据，请检查Cookie是否有效或公众号近期有无发文。")
    
    except Exception as e:
        print(f"❌ 抓取过程出错: {e}")
        traceback.print_exc()

def excel_auto_crawler_entry():
    """从Excel启动全自动爬取流程的入口"""
    print("\n" + "="*60)
    print("🤖 启动Excel全自动爬取流程...")
    print("="*60)
    print("💡 此功能将: ")
    print("   1. 从 `target_articles.xlsx` 读取目标公众号列表。")
    print("   2. 自动操作微信，打开文章链接以获取最新的Cookie。")
    print("   3. 使用获取的Cookie，批量爬取列表中所有公众号的文章。")
    
    confirm = input("是否确认开始？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 操作已取消。")
        return
        
    try:
        # 初始化并运行爬虫
        crawler = ExcelAutoCrawler()
        crawler.auto_crawl_from_excel()
        print("\n✅ Excel自动化爬取流程执行完毕。请查看上方的日志输出获取详细信息。")
        
    except ImportError:
        print("\n❌ 关键库 `uiautomation` 未安装，无法执行此功能。")
        print("💡 请先运行 `pip install uiautomation` 和 `pip install pyperclip`。")
    except Exception as e:
        print(f"\n❌ 自动化爬取过程中发生未知错误: {e}")
        traceback.print_exc()

def main():
    """主程序入口"""
    while True:
        show_menu()
        choice = input("\n请选择功能 (1-4): ").strip()
        if choice == '1':
            extract_cookies()
        elif choice == '2':
            batch_readnum_crawler()
        elif choice == '3':
            excel_auto_crawler_entry()
        elif choice == '4':
            print("👋 感谢使用，再见！")
            break
        else:
            print("❌ 无效选择，请输入1-4。")

if __name__ == '__main__':
    main()