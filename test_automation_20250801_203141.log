2025-08-01 20:31:41,233 - INFO - 🚀 开始自动化流程测试
2025-08-01 20:31:41,233 - INFO - 测试时间: 2025-08-01 20:31:41
2025-08-01 20:31:41,234 - INFO - ==================================================
2025-08-01 20:31:41,234 - INFO - 测试1: Python环境检查
2025-08-01 20:31:41,234 - INFO - ==================================================
2025-08-01 20:31:41,234 - INFO - Python版本: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-01 20:31:41,234 - INFO - Python路径: D:\mynj\mynj_env\Scripts\python.exe
2025-08-01 20:31:41,234 - INFO - 当前工作目录: D:\mynj\wechat_spider
2025-08-01 20:31:41,234 - INFO - ==================================================
2025-08-01 20:31:41,235 - INFO - 测试2: 必需文件检查
2025-08-01 20:31:41,235 - INFO - ==================================================
2025-08-01 20:31:41,235 - INFO - ✅ main_enhanced.py
2025-08-01 20:31:41,235 - INFO - ✅ excel_auto_crawler.py
2025-08-01 20:31:41,235 - INFO - ✅ wechat_browser_automation.py
2025-08-01 20:31:41,235 - INFO - ✅ read_cookie.py
2025-08-01 20:31:41,235 - INFO - ✅ batch_readnum_spider.py
2025-08-01 20:31:41,235 - INFO - ✅ cookie_extractor.py
2025-08-01 20:31:41,235 - INFO - ✅ target_articles.xlsx
2025-08-01 20:31:41,236 - INFO - ✅ requirements.txt
2025-08-01 20:31:41,236 - INFO - 所有必需文件都存在
2025-08-01 20:31:41,236 - INFO - ==================================================
2025-08-01 20:31:41,236 - INFO - 测试3: Python依赖包检查
2025-08-01 20:31:41,236 - INFO - ==================================================
2025-08-01 20:31:41,633 - INFO - ✅ requests
2025-08-01 20:31:41,787 - INFO - ✅ pandas
2025-08-01 20:31:41,860 - INFO - ✅ openpyxl
2025-08-01 20:31:41,860 - ERROR - ❌ beautifulsoup4
2025-08-01 20:31:41,861 - INFO - ✅ mitmproxy
2025-08-01 20:31:41,981 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-01 20:31:41,982 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-01 20:31:41,985 - INFO - ✅ uiautomation
2025-08-01 20:31:41,994 - INFO - ✅ pyperclip
2025-08-01 20:31:41,995 - ERROR - 缺少依赖包: ['beautifulsoup4']
2025-08-01 20:31:41,995 - ERROR - 请运行: pip install -r requirements.txt
2025-08-01 20:31:41,995 - INFO - ==================================================
2025-08-01 20:31:41,995 - INFO - 测试4: Excel数据文件检查
2025-08-01 20:31:41,995 - INFO - ==================================================
2025-08-01 20:31:42,000 - INFO - Excel文件读取成功
2025-08-01 20:31:42,000 - INFO - 列名: ['公众号名称', '示例文章链接', '备注']
2025-08-01 20:31:42,000 - INFO - 数据行数: 1
2025-08-01 20:31:42,000 - INFO - ✅ 第1行: 南京发布
2025-08-01 20:31:42,000 - INFO - 有效数据行数: 1
2025-08-01 20:31:42,001 - INFO - ==================================================
2025-08-01 20:31:42,001 - INFO - 测试5: 微信UI自动化模块检查
2025-08-01 20:31:42,001 - INFO - ==================================================
2025-08-01 20:31:42,001 - INFO - ✅ WeChatBrowserAutomation实例化成功
2025-08-01 20:31:42,001 - INFO - 正在查找微信主窗口...
2025-08-01 20:31:42,076 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-01 20:31:42,076 - INFO - ✅ 找到微信PC版窗口
2025-08-01 20:31:42,076 - INFO - ==================================================
2025-08-01 20:31:42,076 - INFO - 测试6: Cookie抓取器检查
2025-08-01 20:31:42,076 - INFO - ==================================================
2025-08-01 20:31:42,076 - INFO - ✅ ReadCookie实例化成功
2025-08-01 20:31:42,077 - INFO - ℹ️ 未发现现有Cookie，需要重新抓取
2025-08-01 20:31:42,077 - INFO - ==================================================
2025-08-01 20:31:42,077 - INFO - 测试7: 批量爬虫模块检查
2025-08-01 20:31:42,077 - INFO - ==================================================
2025-08-01 20:31:42,159 - INFO - ✅ BatchReadnumSpider实例化成功
2025-08-01 20:31:42,160 - INFO - ============================================================
2025-08-01 20:31:42,160 - INFO - 🏁 测试结果汇总
2025-08-01 20:31:42,160 - INFO - ============================================================
2025-08-01 20:31:42,160 - INFO - 总测试数: 7
2025-08-01 20:31:42,160 - INFO - 通过测试: 6
2025-08-01 20:31:42,160 - INFO - 失败测试: 1
2025-08-01 20:31:42,160 - ERROR - ❌ 部分测试失败，请解决上述问题后重新测试。
