# 微信公众号全自动爬取系统 v3.0

一个完全自动化的微信公众号文章内容和阅读量爬取系统，专为Windows环境设计，支持无人工干预的定时执行。

## 🚀 核心特性

### 全自动化流程
- **零人工干预**：一次配置，自动执行，无需任何控制台输入
- **智能UI自动化**：自动操作微信PC版，获取Cookie和访问文章
- **后台代理抓取**：使用mitmproxy自动拦截和提取认证信息
- **批量数据处理**：支持多个公众号的并行爬取和数据整合

### 企业级功能
- **Windows任务计划程序集成**：支持定时自动执行
- **完整日志系统**：详细记录每个步骤的执行状态
- **错误恢复机制**：自动重试和故障处理
- **多格式数据导出**：Excel和JSON格式结果输出

### 数据采集能力
- 文章标题、链接、发布时间
- 阅读量、点赞数、分享数
- 公众号基本信息
- 自定义时间范围和页数控制

## 📋 系统要求

### 基础环境
- **操作系统**：Windows 10/11
- **Python版本**：3.7+
- **微信PC版**：最新版本，保持登录状态

### 网络配置
- 微信PC版代理设置：`127.0.0.1:8080`
- 确保网络连接稳定

## 🛠️ 安装配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd wechat_spider
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置微信代理
1. 打开微信PC版 → 设置 → 网络
2. 代理类型：HTTP代理
3. 代理地址：`127.0.0.1`
4. 代理端口：`8080`

### 4. 准备Excel数据文件
创建 `target_articles.xlsx` 文件，包含以下列：
- **公众号名称**：要爬取的公众号名称
- **示例文章链接**：该公众号的任意一篇有效文章链接

示例：
| 公众号名称 | 示例文章链接 |
|-----------|-------------|
| 南京发布 | https://mp.weixin.qq.com/s/... |

## 🎯 使用方法

### 方法1：直接运行（推荐）
```bash
python main_enhanced.py
```
程序将自动执行全自动化爬取流程。

### 方法2：使用批处理文件
```bash
run_auto_crawler.bat
```

### 方法3：使用PowerShell脚本
```bash
powershell -ExecutionPolicy Bypass -File run_auto_crawler.ps1
```

### 方法4：交互式模式（调试用）
```bash
python main_enhanced.py --interactive
```

## ⏰ Windows任务计划程序配置

### 快速配置
1. 打开任务计划程序（`taskschd.msc`）
2. 创建基本任务：`微信公众号自动爬取`
3. 触发器：每天凌晨2点
4. 操作：启动程序 `D:\mynj\wechat_spider\run_auto_crawler.bat`
5. 高级设置：
   - ✅ 不管用户是否登录都要运行
   - ✅ 使用最高权限运行
   - ❌ 只有在计算机使用交流电源时才启动

详细配置说明请参考：[Windows任务计划程序配置说明.md](Windows任务计划程序配置说明.md)

## 📊 输出结果

### 文件位置
- **Excel文件**：`data/readnum_batch/readnum_batch_YYYYMMDD_HHMMSS.xlsx`
- **JSON文件**：`data/readnum_batch/readnum_batch_YYYYMMDD_HHMMSS.json`
- **日志文件**：`logs/wechat_spider_YYYYMMDD_HHMMSS.log`

### 数据字段
- 公众号名称
- 文章标题
- 文章链接
- 发布时间
- 阅读量
- 点赞数
- 分享数
- 爬取时间

## 🔧 测试和调试

### 运行系统测试
```bash
python test_automation.py
```
该脚本将检查：
- Python环境和依赖包
- 必需文件完整性
- Excel数据有效性
- 微信UI自动化功能
- Cookie抓取器状态

### 查看日志
- 实时日志：控制台输出
- 历史日志：`logs/` 目录
- 任务计划程序日志：Windows事件查看器

## 📁 项目结构

```
wechat_spider/
├── main_enhanced.py              # 主程序入口（v3.0全自动版）
├── excel_auto_crawler.py         # Excel自动化爬取器
├── wechat_browser_automation.py  # 微信UI自动化模块
├── read_cookie.py                # Cookie读取和管理
├── batch_readnum_spider.py       # 批量爬虫引擎
├── cookie_extractor.py           # mitmproxy Cookie提取器
├── target_articles.xlsx          # 目标公众号配置文件
├── requirements.txt              # Python依赖列表
├── run_auto_crawler.bat          # Windows批处理启动脚本
├── run_auto_crawler.ps1          # PowerShell启动脚本
├── test_automation.py            # 系统测试脚本
├── Windows任务计划程序配置说明.md  # 详细配置指南
├── data/                         # 数据输出目录
│   └── readnum_batch/           # 批量爬取结果
├── logs/                        # 日志文件目录
└── debug/                       # 调试文件目录
```

## ⚠️ 重要注意事项

### 使用规范
- 遵守微信平台使用条款和robots.txt
- 合理控制爬取频率，避免对服务器造成压力
- 仅用于学习研究目的，不得用于商业用途

### 安全建议
- 定期更新微信PC版客户端
- 保护好微信账号安全
- 定期备份重要数据和配置

### 故障排除
1. **Cookie获取失败**：检查微信代理设置和mitmproxy状态
2. **UI自动化失败**：确认微信PC版登录状态和窗口可见性
3. **任务计划程序不执行**：检查用户权限和触发器设置
4. **数据爬取失败**：验证Excel文件中的文章链接有效性

## 📞 技术支持

如遇到问题，请：
1. 首先运行 `python test_automation.py` 进行系统检查
2. 查看 `logs/` 目录中的详细日志
3. 参考 [Windows任务计划程序配置说明.md](Windows任务计划程序配置说明.md)
4. 提交Issue时请附上相关日志信息

## 📄 更新日志

### v3.0 (2025-08-01)
- 🎉 全新的全自动化架构
- 🤖 零人工干预的执行流程
- 📅 Windows任务计划程序完美集成
- 📝 完整的日志系统和错误处理
- 🧪 新增系统测试脚本
- 📚 详细的配置和使用文档

### v2.5
- 重构UI自动化流程
- 提升稳定性和用户体验

### v2.0
- 集成微信UI自动化
- 实现从Excel启动的批量爬取

## 📜 许可证

本项目仅供学习和研究使用。使用本工具时请遵守相关法律法规和平台使用条款。
