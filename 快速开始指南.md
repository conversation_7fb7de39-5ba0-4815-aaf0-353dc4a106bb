# 微信公众号全自动爬取系统 - 快速开始指南

## 🎯 一分钟快速上手

### 第一步：环境准备
1. 确保Python 3.7+已安装
2. 安装依赖：`pip install -r requirements.txt`
3. 确保微信PC版已登录

### 第二步：配置微信代理
1. 打开微信PC版 → 设置 → 网络
2. 代理类型：HTTP代理
3. 代理地址：`127.0.0.1`，端口：`8080`

### 第三步：准备数据文件
编辑 `target_articles.xlsx`，填入要爬取的公众号信息：

| 公众号名称 | 示例文章链接 |
|-----------|-------------|
| 南京发布 | https://mp.weixin.qq.com/s/... |

### 第四步：运行程序
```bash
python main_enhanced.py
```

程序将自动：
1. 启动mitmproxy代理服务器
2. 自动操作微信打开文章获取Cookie
3. 批量爬取所有公众号的文章数据
4. 保存结果到 `data/readnum_batch/` 目录

## 🔧 其他运行方式

### 使用批处理文件
```bash
run_auto_crawler.bat
```

### 使用PowerShell脚本
```bash
powershell -ExecutionPolicy Bypass -File run_auto_crawler.ps1
```

### 交互式调试模式
```bash
python main_enhanced.py --interactive
```

## 📊 查看结果

爬取完成后，结果文件保存在：
- Excel格式：`data/readnum_batch/readnum_batch_YYYYMMDD_HHMMSS.xlsx`
- JSON格式：`data/readnum_batch/readnum_batch_YYYYMMDD_HHMMSS.json`
- 日志文件：`logs/wechat_spider_YYYYMMDD_HHMMSS.log`

## ⏰ 设置定时任务

### Windows任务计划程序
1. 打开任务计划程序（`taskschd.msc`）
2. 创建基本任务：`微信公众号自动爬取`
3. 触发器：每天凌晨2点
4. 操作：启动程序 `D:\mynj\wechat_spider\run_auto_crawler.bat`

详细配置请参考：[Windows任务计划程序配置说明.md](Windows任务计划程序配置说明.md)

## 🧪 测试系统

运行系统测试，确保所有组件正常：
```bash
python test_automation.py
```

## ❓ 常见问题

### Q: Cookie获取失败怎么办？
A: 检查微信代理设置是否正确，确保设置为 `127.0.0.1:8080`

### Q: UI自动化失败怎么办？
A: 确保微信PC版已登录且窗口可见，不要最小化

### Q: 任务计划程序不执行怎么办？
A: 检查用户权限设置，确保勾选"不管用户是否登录都要运行"

### Q: 数据爬取失败怎么办？
A: 检查Excel文件中的文章链接是否有效，确保是微信公众号文章链接

## 📞 获取帮助

1. 查看详细日志：`logs/` 目录
2. 运行系统测试：`python test_automation.py`
3. 参考完整文档：[README.md](README.md)

## ⚠️ 重要提醒

- 仅供学习研究使用
- 遵守微信平台使用条款
- 合理控制爬取频率
- 保护好微信账号安全
