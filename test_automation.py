# coding:utf-8
# test_automation.py
"""
自动化流程测试脚本
用于验证全自动化爬取流程的各个组件是否正常工作
"""

import os
import sys
import logging
import traceback
from datetime import datetime

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'test_automation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
        ]
    )
    return logging.getLogger()

def test_python_environment():
    """测试Python环境"""
    logger = logging.getLogger()
    logger.info("="*50)
    logger.info("测试1: Python环境检查")
    logger.info("="*50)
    
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"Python路径: {sys.executable}")
    logger.info(f"当前工作目录: {os.getcwd()}")
    
    return True

def test_required_files():
    """测试必需文件是否存在"""
    logger = logging.getLogger()
    logger.info("="*50)
    logger.info("测试2: 必需文件检查")
    logger.info("="*50)
    
    required_files = [
        'main_enhanced.py',
        'excel_auto_crawler.py',
        'wechat_browser_automation.py',
        'read_cookie.py',
        'batch_readnum_spider.py',
        'cookie_extractor.py',
        'target_articles.xlsx',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            logger.info(f"✅ {file}")
        else:
            logger.error(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"缺少必需文件: {missing_files}")
        return False
    
    logger.info("所有必需文件都存在")
    return True

def test_dependencies():
    """测试Python依赖包"""
    logger = logging.getLogger()
    logger.info("="*50)
    logger.info("测试3: Python依赖包检查")
    logger.info("="*50)
    
    required_packages = [
        'requests',
        'pandas',
        'openpyxl',
        'beautifulsoup4',
        'mitmproxy',
        'uiautomation',
        'pyperclip'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package}")
        except ImportError:
            logger.error(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {missing_packages}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    
    logger.info("所有依赖包都已安装")
    return True

def test_excel_data():
    """测试Excel数据文件"""
    logger = logging.getLogger()
    logger.info("="*50)
    logger.info("测试4: Excel数据文件检查")
    logger.info("="*50)
    
    try:
        import pandas as pd
        df = pd.read_excel('target_articles.xlsx')
        
        logger.info(f"Excel文件读取成功")
        logger.info(f"列名: {df.columns.tolist()}")
        logger.info(f"数据行数: {len(df)}")
        
        required_columns = ['公众号名称', '示例文章链接']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.error(f"Excel文件缺少必要列: {missing_columns}")
            return False
        
        # 检查数据有效性
        valid_rows = 0
        for index, row in df.iterrows():
            account_name = str(row['公众号名称']).strip()
            article_url = str(row['示例文章链接']).strip()
            if account_name and 'mp.weixin.qq.com' in article_url:
                valid_rows += 1
                logger.info(f"✅ 第{index+1}行: {account_name}")
            else:
                logger.warning(f"⚠️ 第{index+1}行数据无效: {account_name}")
        
        if valid_rows == 0:
            logger.error("Excel文件中没有有效的数据行")
            return False
        
        logger.info(f"有效数据行数: {valid_rows}")
        return True
        
    except Exception as e:
        logger.error(f"Excel文件测试失败: {e}")
        return False

def test_wechat_automation():
    """测试微信UI自动化模块"""
    logger = logging.getLogger()
    logger.info("="*50)
    logger.info("测试5: 微信UI自动化模块检查")
    logger.info("="*50)
    
    try:
        from wechat_browser_automation import WeChatBrowserAutomation, UI_AUTOMATION_AVAILABLE
        
        if not UI_AUTOMATION_AVAILABLE:
            logger.error("uiautomation库不可用")
            return False
        
        automation = WeChatBrowserAutomation()
        logger.info("✅ WeChatBrowserAutomation实例化成功")
        
        # 尝试查找微信窗口（不执行实际操作）
        wechat_window = automation.find_wechat_window()
        if wechat_window:
            logger.info("✅ 找到微信PC版窗口")
        else:
            logger.warning("⚠️ 未找到微信PC版窗口，请确保微信已启动并登录")
        
        return True
        
    except Exception as e:
        logger.error(f"微信UI自动化模块测试失败: {e}")
        return False

def test_cookie_extractor():
    """测试Cookie抓取器"""
    logger = logging.getLogger()
    logger.info("="*50)
    logger.info("测试6: Cookie抓取器检查")
    logger.info("="*50)
    
    try:
        from read_cookie import ReadCookie
        
        cookie_reader = ReadCookie()
        logger.info("✅ ReadCookie实例化成功")
        
        # 检查是否有现有的Cookie
        existing_cookies = cookie_reader.get_latest_cookies()
        if existing_cookies:
            logger.info(f"✅ 发现现有Cookie，时间戳: {existing_cookies.get('timestamp')}")
        else:
            logger.info("ℹ️ 未发现现有Cookie，需要重新抓取")
        
        return True
        
    except Exception as e:
        logger.error(f"Cookie抓取器测试失败: {e}")
        return False

def test_batch_spider():
    """测试批量爬虫"""
    logger = logging.getLogger()
    logger.info("="*50)
    logger.info("测试7: 批量爬虫模块检查")
    logger.info("="*50)
    
    try:
        from batch_readnum_spider import BatchReadnumSpider
        
        spider = BatchReadnumSpider()
        logger.info("✅ BatchReadnumSpider实例化成功")
        
        return True
        
    except Exception as e:
        logger.error(f"批量爬虫模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = setup_test_logging()
    
    logger.info("🚀 开始自动化流程测试")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Python环境", test_python_environment),
        ("必需文件", test_required_files),
        ("Python依赖包", test_dependencies),
        ("Excel数据文件", test_excel_data),
        ("微信UI自动化", test_wechat_automation),
        ("Cookie抓取器", test_cookie_extractor),
        ("批量爬虫模块", test_batch_spider)
    ]
    
    passed_tests = 0
    failed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                failed_tests += 1
        except Exception as e:
            logger.error(f"测试 '{test_name}' 执行时发生异常: {e}")
            logger.error(traceback.format_exc())
            failed_tests += 1
    
    logger.info("="*60)
    logger.info("🏁 测试结果汇总")
    logger.info("="*60)
    logger.info(f"总测试数: {len(tests)}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {failed_tests}")
    
    if failed_tests == 0:
        logger.info("🎉 所有测试通过！系统已准备好执行自动化爬取。")
        return True
    else:
        logger.error("❌ 部分测试失败，请解决上述问题后重新测试。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
